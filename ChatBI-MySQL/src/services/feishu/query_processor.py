"""
飞书查询处理器模块
负责协调整个查询处理流程
"""

import asyncio
import queue
from datetime import datetime
from src.utils.logger import logger
from src.services.feishu.message_apis import (
    reply_simple_text_message,
    send_updates_to_card,
    send_finished_message_to_card,
    get_message_content,
    MessageContent,
)
from src.services.feishu.config import FeishuConfig
from src.services.agent.base_query_processor import BaseQueryProcessor, QueryRequest, THREAD_POOL
from .conversation_service import ConversationService
from .card_service import CardService


class FeishuQueryProcessor(BaseQueryProcessor):
    """飞书查询处理器 - 继承BaseQueryProcessor复用核心逻辑"""

    def __init__(self):
        super().__init__(THREAD_POOL)

    async def process_stream_events(self, result, message_queue: queue.Queue, user_obj) -> tuple:
        """飞书流事件处理逻辑 - 复用基类的统一处理"""
        # 直接使用基类的统一流事件处理逻辑
        return await super().process_stream_events(result, message_queue, user_obj)

    def handle_message_output(self, message: dict) -> str:
        """实现飞书特有的消息输出格式"""
        return message.get("content", "")

    @staticmethod
    async def handle_agent_query(
        message_id: str,
        user_query: str,
        user_info_dict: dict,
        root_id: str = None,
        parent_id: str = None,
        image_url: str = None,
    ):
        """处理用户查询并将更新流式传输到飞书卡片"""
        processor = FeishuQueryProcessor()

        # 飞书特有的前置处理
        sequence = 0
        card_id = None
        element_id = None

        try:
            user_name = user_info_dict.get("name", "unknown_user")
            user_id = user_info_dict.get("open_id", "unknown_user")
            user_email = user_info_dict.get("email", "unknown")
            user_id_hash_code = user_id[-10:]

            logger.info(f"开始处理用户 {user_name} 的查询: {user_query}")

            # 使用root_id作为conversation_id，如果没有则使用message_id
            conversation_id = root_id if root_id else message_id
            logger.info(f"是否使用了root_id作为conversation_id: {conversation_id == root_id}")

            # 1. 验证对话所有权
            is_owner, is_exists = (
                await ConversationService.validate_conversation_ownership(
                    user_name, user_email, message_id, conversation_id
                )
            )
            if not is_owner and is_exists:
                logger.warning(f"对话已经存在，且不属于当前用户:{conversation_id}, {user_name} 跳过处理")
                return

            parent_conent = None
            if not is_exists and not is_owner:
                new_conversation_id = (
                    f"{root_id}_{user_id_hash_code}" if root_id else f"{message_id}_{user_id_hash_code}"
                )
                logger.warning(f"对话ID:{conversation_id}不属于当前用户:{user_name}, 将为他创建新的会话:{new_conversation_id}")
                conversation_id = new_conversation_id
                if parent_id:
                    logger.info(f"尝试获取父消息 {parent_id} 的内容")
                    parent_conent: MessageContent = get_message_content(parent_id)
                    if parent_conent and parent_conent.text:
                        logger.info(
                            f"使用父消息 {parent_id} 的内容作为上下文: {parent_conent.text}"
                        )
                        user_query = f"{parent_conent.text}, {user_query}"

            # 2. 尽早创建初始卡片，让用户快速收到响应
            card_id, element_id = await CardService.create_initial_card_early(
                message_id, user_query, conversation_id, user_id
            )
            if not card_id:
                reply_simple_text_message(message_id, f"卡片创建失败，无法处理您的查询:\n{user_query}\n请稍后再试")
                return

            # 3. 保存用户消息到历史记录
            await ConversationService.save_user_message_to_history(
                user_name, user_email, conversation_id, user_query
            )

            # 准备图片列表
            images = [image_url] if image_url else (parent_conent.image_url if parent_conent and parent_conent.image_url else None)
            if images and images[0] is None:
                images = None

            # 创建查询请求对象
            request = QueryRequest(
                user_query=user_query,
                user_info=user_info_dict,
                conversation_id=conversation_id,
                images=images
            )

            # 使用基类的流式处理功能，但集成飞书卡片更新
            await processor._process_feishu_query(request, card_id, message_id)

        except Exception as e:
            await FeishuQueryProcessor._handle_error(
                e, message_id, card_id, element_id, sequence
            )

    async def _process_feishu_query(self, request: QueryRequest, card_id: str, message_id: str):
        """处理飞书查询的核心逻辑，集成卡片更新"""
        # 创建消息队列
        message_queue = queue.Queue()

        # 创建异步工作函数
        async_worker = self.create_async_worker(request, message_queue)

        # 在线程池中执行异步工作
        self.thread_pool.submit(async_worker)

        # 处理流式响应并更新卡片
        await self._handle_feishu_stream_response(
            request, message_queue, card_id
        )

    async def _handle_feishu_stream_response(self, request: QueryRequest,
                                           message_queue: queue.Queue,
                                           card_id: str):
        """处理飞书流式响应，更新卡片"""
        full_response = ""
        full_logs = ""
        assistant_timestamp = None
        final_input_list = None
        bot_instance = None
        used_agents_list = []
        sequence = 1

        # 卡片更新控制变量
        accumulated_content = ""  # 累积的新内容
        message_steps = FeishuConfig.get_message_steps()  # 获取配置的步长（默认15字符）

        try:
            while True:
                try:
                    message = message_queue.get(timeout=600)
                except queue.Empty:
                    logger.warning("消息队列获取超时")
                    break

                if message is None:
                    # 处理结束，发送最后的累积内容
                    if accumulated_content:
                        sequence = await CardService.send_thinking_and_reply_updates(
                            card_id, full_response, sequence
                        )

                    # 保存结果
                    await self._save_feishu_result(
                        request, full_response, full_logs, assistant_timestamp,
                        final_input_list, bot_instance, used_agents_list
                    )
                    # 完成卡片
                    await CardService.finish_card(card_id, sequence, request.conversation_id)
                    break

                if isinstance(message, dict):
                    msg_type = message.get("type")

                    # 处理特殊事件类型
                    if msg_type == "final_result":
                        final_input_list = message.get("data")
                        continue
                    elif msg_type == "bot_instance":
                        bot_instance = message.get("data")
                        continue
                    elif msg_type == "used_agents":
                        used_agents_list = message.get("data", [])
                        continue

                    # 设置时间戳
                    if not assistant_timestamp and msg_type != "error":
                        assistant_timestamp = int(datetime.now().timestamp() * 1000)

                    # 累积内容
                    if msg_type == "data":
                        content = message.get("content", "")
                        full_response += content
                        accumulated_content += content

                        # 检查是否需要发送更新（基于增量内容长度）
                        if len(accumulated_content) >= message_steps:
                            sequence = await CardService.send_thinking_and_reply_updates(
                                card_id, full_response, sequence
                            )
                            accumulated_content = ""  # 重置累积内容

                    elif msg_type not in ["data", "final_result"] and message.get("content"):
                        log_line = f"[{msg_type.upper()}] {message.get('content', '')}"
                        full_logs += log_line + "\n"

        except (GeneratorExit, ConnectionResetError, BrokenPipeError) as e:
            logger.warning(f"飞书客户端断开连接: {e}")
            # 处理客户端断开，提交后台任务
            self.handle_client_disconnect(request)
        except Exception as e:
            logger.exception(f"处理飞书流式响应时出错: {e}")
            # 发送错误到卡片
            await self._send_error_to_card(card_id, str(e), sequence)

    async def _save_feishu_result(self, request: QueryRequest, response: str, logs: str,
                                timestamp: int, final_input_list, bot_instance, used_agents_list):
        """保存飞书查询结果 - 使用统一的保存机制"""
        if timestamp:
            # 使用基类的统一保存方法，确保与API接口保持一致
            self.save_assistant_response(
                request.user_info, request.conversation_id, response, logs,
                timestamp, final_input_list, used_agents_list, bot_instance
            )
            logger.info(f"飞书查询结果已保存到对话 {request.conversation_id}")

    async def _send_error_to_card(self, card_id: str, error_msg: str, sequence: int):
        """发送错误消息到卡片"""
        try:
            error_message = f"处理您的请求时发生内部错误。\n错误信息: {error_msg}"
            await asyncio.to_thread(
                send_updates_to_card,
                card_id,
                error_message,
                None,
                sequence=sequence + 1,
            )
        except Exception as e:
            logger.error(f"发送错误消息到卡片时出错: {e}")







    @staticmethod
    async def _handle_error(
        error, message_id, card_id=None, element_id=None, sequence=0
    ):
        """处理查询过程中的错误

        Args:
            error: 错误对象
            message_id: 消息ID
            card_id: 卡片ID
            element_id: 元素ID
            sequence: 序列号
        """
        logger.error(f"处理Agent查询时出错: {error}", exc_info=True)

        # 尝试发送错误消息到卡片，如果可能的话，否则回复文本
        try:
            error_message = f"处理您的请求时发生内部错误。\n错误信息: {error}"
            if card_id:
                error_sequence = sequence + 1
                await asyncio.to_thread(
                    send_updates_to_card,
                    card_id,
                    error_message,
                    element_id,
                    sequence=error_sequence,
                )
                finish_sequence = error_sequence + 1
                await asyncio.to_thread(
                    send_finished_message_to_card,
                    card_id,
                    sequence=finish_sequence,
                    chat_id="invalid_chat_id",
                )
            else:
                reply_simple_text_message(message_id, error_message)
        except Exception as send_error:
            logger.error(f"发送错误消息到飞书时再次出错: {send_error}")
            reply_simple_text_message(
                message_id, "处理您的请求时发生内部错误，并且无法更新卡片状态。"
            )


# 保持向后兼容性
QueryProcessor = FeishuQueryProcessor
